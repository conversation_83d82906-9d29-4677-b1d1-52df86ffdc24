using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Application.Mappings;
using EAMS.Application.Services;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace EAMS.Tests;

public class AccommodationApplicationServiceTests
{
    private readonly Mock<IAccommodationDomainService> _mockDomainService;
    private readonly Mock<IRepository<AccommodationType, int>> _mockAccommodationTypeRepo;
    private readonly Mock<IRepository<Density, int>> _mockDensityRepo;
    private readonly Mock<IRepository<Duration, int>> _mockDurationRepo;
    private readonly Mock<IRepository<Region, int>> _mockRegionRepo;
    private readonly IMapper _mapper;
    private readonly AccommodationApplicationService _service;

    public AccommodationApplicationServiceTests()
    {
        _mockDomainService = new Mock<IAccommodationDomainService>();
        _mockAccommodationTypeRepo = new Mock<IRepository<AccommodationType, int>>();
        _mockDensityRepo = new Mock<IRepository<Density, int>>();
        _mockDurationRepo = new Mock<IRepository<Duration, int>>();
        _mockRegionRepo = new Mock<IRepository<Region, int>>();

        // Setup AutoMapper
        var config = new MapperConfiguration(cfg => cfg.AddProfile<AccommodationProfile>());
        _mapper = config.CreateMapper();

        _service = new AccommodationApplicationService(
            _mockDomainService.Object,
            _mapper,
            _mockAccommodationTypeRepo.Object,
            _mockDensityRepo.Object,
            _mockDurationRepo.Object,
            _mockRegionRepo.Object);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnMappedDtos()
    {
        // Arrange
        var accommodations = new List<Accommodation>
        {
            new Accommodation
            {
                Id = 1,
                Name = "Test Hotel",
                StreetLine1 = "123 Test St",
                Suburb = "Test Suburb",
                State = "Test State",
                Postcode = "1234"
            }
        };

        _mockDomainService.Setup(x => x.GetAllAsync())
            .ReturnsAsync(accommodations);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var dto = result.First();
        Assert.Equal("Test Hotel", dto.Name);
        Assert.Equal("123 Test St", dto.StreetLine1);
    }

    [Fact]
    public async Task SearchAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        var accommodations = new List<Accommodation>
        {
            new Accommodation
            {
                Id = 1,
                Name = "Test Hotel",
                StreetLine1 = "123 Test St",
                Suburb = "Test Suburb",
                State = "Test State",
                Postcode = "1234"
            }
        };

        var paginationResult = new PaginationResult<Accommodation>(accommodations, 1, 10, 1);

        _mockDomainService.Setup(x => x.SearchWithPaginationAsync(It.IsAny<SearchCriteria>(), It.IsAny<PaginationRequest>()))
            .ReturnsAsync(paginationResult);

        // Act
        var result = await _service.SearchAsync("test", page: 1, pageSize: 10);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Items);
        Assert.Equal(1, result.Page);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(1, result.TotalCount);
        Assert.Equal(1, result.TotalPages);
    }

    [Fact]
    public async Task GetByIdAsync_WhenExists_ShouldReturnDto()
    {
        // Arrange
        var accommodation = new Accommodation
        {
            Id = 1,
            Name = "Test Hotel",
            StreetLine1 = "123 Test St",
            Suburb = "Test Suburb",
            State = "Test State",
            Postcode = "1234"
        };

        _mockDomainService.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(accommodation);

        // Act
        var result = await _service.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Hotel", result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_WhenNotExists_ShouldReturnNull()
    {
        // Arrange
        _mockDomainService.Setup(x => x.GetByIdAsync(999))
            .ReturnsAsync((Accommodation?)null);

        // Act
        var result = await _service.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }
}
