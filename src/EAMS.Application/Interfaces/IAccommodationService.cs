using EAMS.Application.DTOs;

namespace EAMS.Application.Interfaces;

public interface IAccommodationService
{
    Task<IEnumerable<AccommodationDto>> GetAll();

    Task<AccommodationDto?> GetByIdAsync(long id);

    Task<AccommodationDto> CreateAsync(AccommodationDto accommodationDto);

    Task<AccommodationDto> UpdateAsync(AccommodationDto accommodationDto);

    Task<bool> DeleteAsync(long id);

    Task<List<AccommodationDto> results, int totalCount> SearchAsync(
        string? searchTerm,
        int? accommodationTypeId,
        int? densityId,
        int? durationId,
        int? regionId,
        bool? inactive,
        int pageNumber,
        int pageSize,
        string sortBy,
        string sortDirection);
}
