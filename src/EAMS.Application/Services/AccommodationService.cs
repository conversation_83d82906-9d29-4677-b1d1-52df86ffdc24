using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Domain.Aggregates;
using EAMS.Application.Interfaces;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;

namespace EAMS.Application.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;
    private readonly IMapper _mapper;
    private readonly IRepository<AccommodationType, int> _accommodationTypeRepository;
    private readonly IRepository<Density, int> _densityRepository;
    private readonly IRepository<Duration, int> _durationRepository;
    private readonly IRepository<Region, int> _regionRepository;

    public AccommodationService(
        IAccommodationRepository accommodationRepository,
        IMapper mapper,
        IRepository<AccommodationType, int> accommodationTypeRepository,
        IRepository<Density, int> densityRepository,
        IRepository<Duration, int> durationRepository,
        IRepository<Region, int> regionRepository)
    {
        _accommodationRepository = accommodationRepository;
        _mapper = mapper;
        _accommodationTypeRepository = accommodationTypeRepository;
        _densityRepository = densityRepository;
        _durationRepository = durationRepository;
        _regionRepository = regionRepository;
    }

    public async Task<IEnumerable<AccommodationDto>> GetAll()
    {
        var accommodations = await _accommodationRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
    }

    public async Task<AccommodationDto?> GetByIdAsync(long id)
    {
        var accommodation = await _accommodationRepository.GetByIdAsync(id);
        return accommodation != null ? _mapper.Map<AccommodationDto>(accommodation) : null;
    }

    public async Task<AccommodationDto> CreateAsync(AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        
        // Load related entities if needed
        await LoadRelatedEntities(accommodation, accommodationDto);
        
        var createdAccommodation = await _accommodationRepository.CreateAsync(accommodation);
        return _mapper.Map<AccommodationDto>(createdAccommodation);
    }

    public async Task<AccommodationDto> UpdateAsync(AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        
        // Load related entities if needed
        await LoadRelatedEntities(accommodation, accommodationDto);
        
        var updatedAccommodation = await _accommodationRepository.UpdateAsync(accommodation);
        return _mapper.Map<AccommodationDto>(updatedAccommodation);
    }

    public async Task<bool> DeleteAsync(long id)
    {
        return await _accommodationRepository.DeleteAsync(id);
    }

    public async Task<PaginatedResultDto<AccommodationDto>> SearchAsync(
        string? search = null,
        int? accommodationTypeId = null,
        int? densityId = null,
        int? durationId = null,
        int? regionId = null,
        bool? inactive = null,
        int page = 1,
        int pageSize = 10)
    {
        // Convert parameters to domain value objects
        var searchCriteria = await BuildSearchCriteria(search, accommodationTypeId, densityId, durationId, regionId, inactive);
        var paginationRequest = PaginationRequest.Create(page, pageSize);

        // Call domain service
        var result = await _accommodationService.SearchWithPaginationAsync(searchCriteria, paginationRequest);

        // Convert to DTOs
        var accommodationDtos = _mapper.Map<IEnumerable<AccommodationDto>>(result.Items);

        return new PaginatedResultDto<AccommodationDto>
        {
            Items = accommodationDtos.ToList(),
            Page = result.Page,
            PageSize = result.PageSize,
            TotalCount = result.TotalCount,
            TotalPages = result.TotalPages,
            HasPreviousPage = result.HasPreviousPage,
            HasNextPage = result.HasNextPage
        };
    }

    /// <summary>
    /// Builds search criteria from individual parameters
    /// </summary>
    private async Task<SearchCriteria> BuildSearchCriteria(
        string? search,
        int? accommodationTypeId,
        int? densityId,
        int? durationId,
        int? regionId,
        bool? inactive)
    {
        AccommodationType? accommodationType = null;
        Density? density = null;
        Duration? duration = null;
        Region? region = null;

        // Load entities by ID if provided
        if (accommodationTypeId.HasValue)
            accommodationType = await _accommodationTypeRepository.GetByIdAsync(accommodationTypeId.Value);

        if (densityId.HasValue)
            density = await _densityRepository.GetByIdAsync(densityId.Value);

        if (durationId.HasValue)
            duration = await _durationRepository.GetByIdAsync(durationId.Value);

        if (regionId.HasValue)
            region = await _regionRepository.GetByIdAsync(regionId.Value);

        return new SearchCriteria(search, accommodationType, density, duration, region, inactive);
    }

    /// <summary>
    /// Loads related entities for the accommodation based on DTO data
    /// </summary>
    private async Task LoadRelatedEntities(Accommodation accommodation, AccommodationDto dto)
    {
        // Load AccommodationType
        if (dto.AccommodationType != null && dto.AccommodationType.Id > 0)
        {
            accommodation.AccommodationType = await _accommodationTypeRepository.GetByIdAsync(dto.AccommodationType.Id) 
                ?? throw new ArgumentException($"AccommodationType with ID {dto.AccommodationType.Id} not found");
            accommodation.AccommodationTypeId = accommodation.AccommodationType.Id;
        }

        // Load Density
        if (dto.Density != null && dto.Density.Id > 0)
        {
            accommodation.Density = await _densityRepository.GetByIdAsync(dto.Density.Id) 
                ?? throw new ArgumentException($"Density with ID {dto.Density.Id} not found");
            accommodation.DensityId = accommodation.Density.Id;
        }

        // Load Region
        if (dto.Region != null && dto.Region.Id > 0)
        {
            accommodation.Region = await _regionRepository.GetByIdAsync(dto.Region.Id) 
                ?? throw new ArgumentException($"Region with ID {dto.Region.Id} not found");
            accommodation.RegionId = accommodation.Region.Id;
        }

        // Load Duration entities
        if (dto.Duration != null && dto.Duration.Any())
        {
            var durations = new List<Duration>();
            foreach (var durationDto in dto.Duration)
            {
                if (durationDto.Id > 0)
                {
                    var duration = await _durationRepository.GetByIdAsync(durationDto.Id);
                    if (duration != null)
                        durations.Add(duration);
                }
            }
            accommodation.Duration = durations;
        }
    }
}
