namespace EAMS.Application.DTOs;

/// <summary>
/// DTO for paginated results
/// </summary>
/// <typeparam name="T">The type of items in the result</typeparam>
public class PaginatedResultDto<T>
{
    /// <summary>
    /// The items in the current page
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// Current page number (1-based)
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of items across all pages
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Creates an empty paginated result
    /// </summary>
    public static PaginatedResultDto<T> Empty(int page = 1, int pageSize = 10)
    {
        return new PaginatedResultDto<T>
        {
            Items = new List<T>(),
            Page = page,
            PageSize = pageSize,
            TotalCount = 0,
            TotalPages = 0,
            HasPreviousPage = false,
            HasNextPage = false
        };
    }
}
