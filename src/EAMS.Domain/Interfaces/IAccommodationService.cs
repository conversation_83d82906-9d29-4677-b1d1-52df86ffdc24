using EAMS.Domain.Aggregates;
using EAMS.Domain.ValueObjects;

namespace EAMS.Domain.Interfaces;

public interface IAccommodationService
{
    Task<IEnumerable<Accommodation>> GetAllAsync();

    Task<IEnumerable<Accommodation>> SearchAsync(SearchCriteria criteria);

    Task<PaginationResult<Accommodation>> SearchWithPaginationAsync(SearchCriteria criteria, PaginationRequest pagination);

    Task<Accommodation?> GetByIdAsync(long id);

    Task<Accommodation> CreateAsync(Accommodation accommodation);

    Task<Accommodation> UpdateAsync(Accommodation accommodation);

    Task<bool> DeleteAsync(long id);

    Task<int> GetCountAsync(SearchCriteria criteria);
}
