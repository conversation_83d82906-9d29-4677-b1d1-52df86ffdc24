namespace EAMS.Domain.ValueObjects;

/// <summary>
/// Immutable value object representing pagination request parameters
/// </summary>
public readonly record struct PaginationRequest
{
    public int Page { get; init; }
    public int PageSize { get; init; }
    public int Skip => (Page - 1) * PageSize;

    public PaginationRequest(int page = 1, int pageSize = 10)
    {
        if (page < 1)
            throw new ArgumentException("Page must be greater than 0", nameof(page));
        if (pageSize < 1)
            throw new ArgumentException("Page size must be greater than 0", nameof(pageSize));
        if (pageSize > 100)
            throw new ArgumentException("Page size cannot exceed 100", nameof(pageSize));

        Page = page;
        PageSize = pageSize;
    }

    /// <summary>
    /// Creates a default pagination request (page 1, size 10)
    /// </summary>
    public static PaginationRequest Default => new();

    /// <summary>
    /// Creates a pagination request with specified page and default size
    /// </summary>
    public static PaginationRequest ForPage(int page) => new(page);

    /// <summary>
    /// Creates a pagination request with specified page and size
    /// </summary>
    public static PaginationRequest Create(int page, int pageSize) => new(page, pageSize);
}
