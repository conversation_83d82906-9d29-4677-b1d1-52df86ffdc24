namespace EAMS.Domain.ValueObjects;

/// <summary>
/// Immutable value object representing search criteria for accommodations
/// </summary>
public readonly record struct SearchCriteria
{
    public string? SearchTerm { get; init; }
    public AccommodationType? AccommodationType { get; init; }
    public Density? Density { get; init; }
    public Duration? Duration { get; init; }
    public Region? Region { get; init; }
    public bool? Inactive { get; init; }

    public SearchCriteria(
        string? searchTerm = null,
        AccommodationType? accommodationType = null,
        Density? density = null,
        Duration? duration = null,
        Region? region = null,
        bool? inactive = null)
    {
        SearchTerm = string.IsNullOrWhiteSpace(searchTerm) ? null : searchTerm.Trim();
        AccommodationType = accommodationType;
        Density = density;
        Duration = duration;
        Region = region;
        Inactive = inactive;
    }

    /// <summary>
    /// Checks if any search criteria is specified
    /// </summary>
    public bool HasCriteria => !string.IsNullOrWhiteSpace(SearchTerm) ||
                               AccommodationType != null ||
                               Density != null ||
                               Duration != null ||
                               Region != null ||
                               Inactive != null;

    /// <summary>
    /// Creates a search criteria with only the search term
    /// </summary>
    public static SearchCriteria WithSearchTerm(string searchTerm) => new(searchTerm: searchTerm);

    /// <summary>
    /// Creates an empty search criteria (returns all results)
    /// </summary>
    public static SearchCriteria Empty => new();
}
