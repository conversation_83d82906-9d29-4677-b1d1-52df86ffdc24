namespace EAMS.Domain.ValueObjects;

/// <summary>
/// Immutable value object representing paginated results
/// </summary>
public readonly record struct PaginationResult<T>
{
    public IReadOnlyList<T> Items { get; init; }
    public int Page { get; init; }
    public int PageSize { get; init; }
    public int TotalCount { get; init; }
    public int TotalPages { get; init; }
    public bool HasPreviousPage => Page > 1;
    public bool HasNextPage => Page < TotalPages;

    public PaginationResult(IEnumerable<T> items, int page, int pageSize, int totalCount)
    {
        if (page < 1)
            throw new ArgumentException("Page must be greater than 0", nameof(page));
        if (pageSize < 1)
            throw new ArgumentException("Page size must be greater than 0", nameof(pageSize));
        if (totalCount < 0)
            throw new ArgumentException("Total count cannot be negative", nameof(totalCount));

        Items = items?.ToList().AsReadOnly() ?? new List<T>().AsReadOnly();
        Page = page;
        PageSize = pageSize;
        TotalCount = totalCount;
        TotalPages = totalCount == 0 ? 0 : (int)Math.Ceiling((double)totalCount / pageSize);
    }

    /// <summary>
    /// Creates an empty pagination result
    /// </summary>
    public static PaginationResult<T> Empty(int page = 1, int pageSize = 10) => 
        new(Enumerable.Empty<T>(), page, pageSize, 0);

    /// <summary>
    /// Creates a pagination result from a collection and pagination request
    /// </summary>
    public static PaginationResult<T> Create(IEnumerable<T> items, PaginationRequest pagination, int totalCount) =>
        new(items, pagination.Page, pagination.PageSize, totalCount);
}
