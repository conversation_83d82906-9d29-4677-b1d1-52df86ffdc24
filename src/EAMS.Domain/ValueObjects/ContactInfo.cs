namespace EAMS.Domain.ValueObjects;

/// <summary>
/// Immutable value object representing contact information
/// </summary>
public readonly record struct ContactInfo
{
    public string? Phone { get; init; }
    public string? Email { get; init; }
    public string? Website { get; init; }

    public ContactInfo(string? phone = null, string? email = null, string? website = null)
    {
        Phone = string.IsNullOrWhiteSpace(phone) ? null : phone.Trim();
        Email = string.IsNullOrWhiteSpace(email) ? null : email.Trim();
        Website = string.IsNullOrWhiteSpace(website) ? null : website.Trim();
    }

    /// <summary>
    /// Checks if any contact information is provided
    /// </summary>
    public bool HasContactInfo => !string.IsNullOrWhiteSpace(Phone) || 
                                  !string.IsNullOrWhiteSpace(Email) || 
                                  !string.IsNullOrWhiteSpace(Website);

    /// <summary>
    /// Validates the contact information format
    /// </summary>
    public bool IsValid()
    {
        // Basic email validation if email is provided
        if (!string.IsNullOrWhiteSpace(Email) && !Email.Contains('@'))
            return false;

        // Basic website validation if website is provided
        if (!string.IsNullOrWhiteSpace(Website) && 
            !Website.StartsWith("http://", StringComparison.OrdinalIgnoreCase) && 
            !Website.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            return false;

        return true;
    }
}
