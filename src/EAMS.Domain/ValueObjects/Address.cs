namespace EAMS.Domain.ValueObjects;

/// <summary>
/// Immutable value object representing an address
/// </summary>
public readonly record struct Address
{
    public string StreetLine1 { get; init; }
    public string? StreetLine2 { get; init; }
    public string Suburb { get; init; }
    public string State { get; init; }
    public string Postcode { get; init; }

    public Address(string streetLine1, string suburb, string state, string postcode, string? streetLine2 = null)
    {
        if (string.IsNullOrWhiteSpace(streetLine1))
            throw new ArgumentException("Street line 1 cannot be empty", nameof(streetLine1));
        if (string.IsNullOrWhiteSpace(suburb))
            throw new ArgumentException("Suburb cannot be empty", nameof(suburb));
        if (string.IsNullOrWhiteSpace(state))
            throw new ArgumentException("State cannot be empty", nameof(state));
        if (string.IsNullOrWhiteSpace(postcode))
            throw new ArgumentException("Postcode cannot be empty", nameof(postcode));

        StreetLine1 = streetLine1.Trim();
        StreetLine2 = string.IsNullOrWhiteSpace(streetLine2) ? null : streetLine2.Trim();
        Suburb = suburb.Trim();
        State = state.Trim();
        Postcode = postcode.Trim();
    }

    /// <summary>
    /// Gets the full address as a formatted string
    /// </summary>
    public string GetFullAddress()
    {
        var parts = new List<string> { StreetLine1 };
        
        if (!string.IsNullOrWhiteSpace(StreetLine2))
            parts.Add(StreetLine2);
            
        parts.Add(Suburb);
        parts.Add($"{State} {Postcode}");
        
        return string.Join(", ", parts);
    }

    /// <summary>
    /// Checks if the address contains the search term in any field
    /// </summary>
    public bool ContainsSearchTerm(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return true;

        var term = searchTerm.ToLowerInvariant();
        
        return StreetLine1.ToLowerInvariant().Contains(term) ||
               (StreetLine2?.ToLowerInvariant().Contains(term) ?? false) ||
               Suburb.ToLowerInvariant().Contains(term) ||
               State.ToLowerInvariant().Contains(term) ||
               Postcode.ToLowerInvariant().Contains(term);
    }
}
