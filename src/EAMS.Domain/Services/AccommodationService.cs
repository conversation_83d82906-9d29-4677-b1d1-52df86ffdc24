using EAMS.Domain.Aggregates;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System.Linq.Expressions;

namespace EAMS.Domain.Services;

/// <summary>
/// Domain service for accommodation business logic
/// </summary>
public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;

    public AccommodationService(IAccommodationRepository accommodationRepository)
    {
        _accommodationRepository = accommodationRepository;
    }

    public async Task<IEnumerable<Accommodation>> GetAllAsync()
    {
        return await _accommodationRepository.GetAllAsync(null,
            includes: [a => a.AccommodationType, 
                     a => a.Density, 
                     a => a.Region, 
                     a => a.Duration, 
                     a => a.AmenityOptions]);
    }

    public async Task<IEnumerable<Accommodation>> SearchAsync(SearchCriteria criteria)
    {
        var predicate = BuildSearchPredicate(criteria);
        
        return await _accommodationRepository.GetAllAsync(
            where: predicate,
            includes: [a => a.AccommodationType, 
                     a => a.Density, 
                     a => a.Region, 
                     a => a.Duration, 
                     a => a.AmenityOptions]);
    }

    public async Task<PaginationResult<Accommodation>> SearchWithPaginationAsync(SearchCriteria criteria, PaginationRequest pagination)
    {
        var predicate = BuildSearchPredicate(criteria);
        
        // Get total count
        var totalCount = await GetCountAsync(criteria);
        
        // Get paginated results
        var allResults = await _accommodationRepository.GetAllAsync(
            where: predicate,
            includes: [a => a.AccommodationType, 
                     a => a.Density, 
                     a => a.Region, 
                     a => a.Duration, 
                     a => a.AmenityOptions]);
        
        var paginatedResults = allResults
            .Skip(pagination.Skip)
            .Take(pagination.PageSize)
            .ToList();

        return new PaginationResult<Accommodation>(paginatedResults, pagination.Page, pagination.PageSize, totalCount);
    }

    public async Task<Accommodation?> GetByIdAsync(long id)
    {
        return await _accommodationRepository.GetByIdAsync(id,
            includes: [a => a.AccommodationType, 
                     a => a.Density, 
                     a => a.Region, 
                     a => a.Duration, 
                     a => a.AmenityOptions]);
    }

    public async Task<Accommodation> CreateAsync(Accommodation accommodation)
    {
        // Business rule: Validate accommodation data
        ValidateAccommodation(accommodation);

        // Set timestamps for new entity
        accommodation.CreatedAt = DateTime.UtcNow;
        accommodation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _accommodationRepository.AddAsync(accommodation);

        // Return the accommodation with its generated ID
        return accommodation;
    }

    public async Task<Accommodation> UpdateAsync(Accommodation accommodation)
    {
        // Business rule: Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodation.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodation.Id);
        }

        // Business rule: Validate accommodation data
        ValidateAccommodation(accommodation);

        // Update timestamp
        accommodation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _accommodationRepository.UpdateAsync(accommodation);

        return accommodation;
    }

    public async Task<bool> DeleteAsync(long id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _accommodationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<int> GetCountAsync(SearchCriteria criteria)
    {
        var predicate = BuildSearchPredicate(criteria);
        var results = await _accommodationRepository.GetAllAsync(where: predicate);
        return results.Count();
    }

    /// <summary>
    /// Builds a predicate expression for searching accommodations based on criteria
    /// </summary>
    private Expression<Func<Accommodation, bool>>? BuildSearchPredicate(SearchCriteria criteria)
    {
        if (!criteria.HasCriteria)
            return null;

        return a =>
            // Search term filter (OR logic within search fields)
            (string.IsNullOrWhiteSpace(criteria.SearchTerm) ||
             a.Name.ToLower().Contains(criteria.SearchTerm.ToLower()) ||
             a.StreetLine1.ToLower().Contains(criteria.SearchTerm.ToLower()) ||
             (a.StreetLine2 != null && a.StreetLine2.ToLower().Contains(criteria.SearchTerm.ToLower())) ||
             a.Suburb.ToLower().Contains(criteria.SearchTerm.ToLower()) ||
             a.Postcode.ToLower().Contains(criteria.SearchTerm.ToLower())) &&

            // Accommodation type filter
            (criteria.AccommodationType == null || a.AccommodationType == criteria.AccommodationType) &&

            // Density filter
            (criteria.Density == null || a.Density == criteria.Density) &&

            // Duration filter
            (criteria.Duration == null || a.Duration.Contains(criteria.Duration)) &&

            // Region filter
            (criteria.Region == null || a.Region == criteria.Region) &&

            // Inactive status filter
            (criteria.Inactive == null || a.Inactive == criteria.Inactive);
    }

    /// <summary>
    /// Validates accommodation business rules
    /// </summary>
    private void ValidateAccommodation(Accommodation accommodation)
    {
        if (string.IsNullOrWhiteSpace(accommodation.Name))
            throw new ArgumentException("Accommodation name is required");

        if (string.IsNullOrWhiteSpace(accommodation.StreetLine1))
            throw new ArgumentException("Street address is required");

        if (string.IsNullOrWhiteSpace(accommodation.Suburb))
            throw new ArgumentException("Suburb is required");

        if (string.IsNullOrWhiteSpace(accommodation.State))
            throw new ArgumentException("State is required");

        if (string.IsNullOrWhiteSpace(accommodation.Postcode))
            throw new ArgumentException("Postcode is required");

        if (accommodation.Duration == null || !accommodation.Duration.Any())
            throw new ArgumentException("At least one duration must be specified");
    }
}
