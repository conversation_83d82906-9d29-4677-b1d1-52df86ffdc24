using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AccommodationsController : ControllerBase
{
    private readonly IAccommodationService _accommodationService;
    private readonly ILogger<AccommodationsController> _logger;

    public AccommodationsController(
        IAccommodationService accommodationService,
        ILogger<AccommodationsController> logger)
    {
        _accommodationService = accommodationService;
        _logger = logger;
    }

    /// <summary>
    /// Get all accommodations
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<IEnumerable<AccommodationDto>>> GetAccommodations()
    {
        var accommodations = await _accommodationService.GetAllAsync();
        return Ok(accommodations);
    }

    /// <summary>
    /// Get accommodation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<AccommodationDto>> GetAccommodation(long id)
    {
        var accommodation = await _accommodationService.GetByIdAsync(id);

        if (accommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", id);
        }

        return Ok(accommodation);
    }

    /// <summary>
    /// Search accommodations with filtering and pagination
    /// </summary>
    /// <param name="search">Optional search term to filter accommodations by name, address, suburb, or postcode</param>
    /// <param name="accommodationTypeId">Optional filter by accommodation type ID</param>
    /// <param name="densityId">Optional filter by density ID</param>
    /// <param name="durationId">Optional filter by duration ID</param>
    /// <param name="regionId">Optional filter by region ID</param>
    /// <param name="inactive">Optional filter by inactive status</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    [HttpGet("search")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<PaginatedResultDto<AccommodationDto>>> SearchAccommodations(
        [FromQuery] string? search = null,
        [FromQuery] int? accommodationTypeId = null,
        [FromQuery] int? densityId = null,
        [FromQuery] int? durationId = null,
        [FromQuery] int? regionId = null,
        [FromQuery] bool? inactive = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var result = await _accommodationService.SearchAsync(
            search, accommodationTypeId, densityId, durationId, regionId, inactive, page, pageSize);

        return Ok(result);
    }

    /// <summary>
    /// Create a new accommodation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<AccommodationDto>> CreateAccommodation(AccommodationDto accommodationDto)
    {
        var createdAccommodation = await _accommodationService.CreateAsync(accommodationDto);
        return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, createdAccommodation);
    }

    /// <summary>
    /// Update an existing accommodation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(long id, AccommodationDto accommodationDto)
    {
        // Ensure the ID in the URL matches the DTO
        accommodationDto.Id = id;

        var updatedAccommodation = await _accommodationService.UpdateAsync(accommodationDto);
        return Ok(updatedAccommodation);
    }

    /// <summary>
    /// Delete an accommodation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<bool>> DeleteAccommodation(long id)
    {
        var result = await _accommodationService.DeleteAsync(id);
        return Ok(result);
    }
}
