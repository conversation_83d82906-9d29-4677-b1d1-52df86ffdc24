using EAMS.Application.Interfaces;
using EAMS.Application.Services;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Repositories;
using EAMS.Domain.Services;
using EAMS.Domain.ValueObjects;
using EAMS.Infrastructure.Repositories;
using Microsoft.OpenApi.Models;
using System.Reflection;

namespace EAMS.API.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddRepositoryServices(this IServiceCollection services)
        {
            services.AddScoped<IRepository<AccommodationType, int>, AccommodationTypeRepository>();
            services.AddScoped<IRepository<AmenityType, int>, AmenityTypeRepository>();
            services.AddScoped<IRepository<Density, int>, DensityRepository>(); 
            services.AddScoped<IRepository<Duration, int>, DurationRepository>();
            services.AddScoped<IRepository<Region, int>, RegionRepository>();
            services.AddScoped<IAccommodationRepository, AccommodationRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IOrganisationRepository, OrganisationRepository>();
            services.AddScoped<IAmenityRepository, AmenityRepository>();
            services.AddScoped<IAmenityOptionsRepository, AmenityOptionsRepository>();
            services.AddScoped<IUserInvitationRepository, UserInvitationRepository>();

            return services;
        }

        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddMemoryCache();

            // Domain Services
            services.AddScoped<IAccommodationService, AccommodationService>();
            services.AddScoped<IAmenityService, AmenityService>();
            services.AddScoped<IGraphService, GraphService>();

            // Application Services
            services.AddScoped<IAccommodationService, AccommodationService>();
            services.AddScoped<IOrganisationService, OrganisationService>();
            services.AddScoped<IAmenityOptionsService, AmenityOptionsService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IValueObjectsService, ValueObjectsService>();

            return services;
        }

        public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new() { Title = "EAMS BFF API", Version = "v1", Description = "An .Net Core API for EAMS Front End operations" });

                // Custom schema ID generator to handle Microsoft Graph type conflicts
                c.CustomSchemaIds(type => type.FullName);

                // OAuth2 authorization
                var tenantId = configuration["AzureAd:TenantId"];
                var authUrl = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/authorize");
                var tokenUrl = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token");
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = authUrl,
                            TokenUrl = tokenUrl,
                            Scopes = new Dictionary<string, string>
                            {
                                {configuration["SwaggerOAuth:Scopes"]!, "Access as user"}
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                        new[] { configuration["SwaggerOAuth:Scopes"]! }
                    }
                });

                var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
            });
            return services;
        }

        /// <summary>
        /// Helper method to generate schema IDs for types, handling generic types recursively
        /// </summary>
        private static string GetSchemaIdForType(Type type)
        {
            // Handle Microsoft Graph Group conflicts
            if (type.FullName == "Microsoft.Graph.Models.Group")
                return "GraphGroup";
            if (type.FullName == "Microsoft.Graph.Models.TermStore.Group")
                return "TermStoreGroup";

            // Handle generic types recursively
            if (type.IsGenericType)
            {
                var genericTypeName = type.Name.Split('`')[0]; // Remove the `1, `2 etc.
                var genericArgs = type.GetGenericArguments()
                    .Select(arg => GetSchemaIdForType(arg))
                    .ToArray();
                return $"{genericTypeName}Of{string.Join("And", genericArgs)}";
            }

            // For other types, use the type name
            return type.Name;
        }
    }
}